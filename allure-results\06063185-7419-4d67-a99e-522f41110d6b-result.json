{"uuid": "06063185-7419-4d67-a99e-522f41110d6b", "historyId": "89e8eb1df9ce0324cadb8967ee96450b:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > negativeLogin.spec.ts > Negative Login Test Cases"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "negativeLogin.spec.ts"}, {"name": "subSuite", "value": "Negative Login Test Cases"}], "links": [], "start": 1752188035114, "name": "TC-NEG-08: Special characters in credentials", "fullName": "negativeLogin.spec.ts#Negative Login Test Cases TC-NEG-08: Special characters in credentials", "testCaseId": "89e8eb1df9ce0324cadb8967ee96450b", "stop": 1752188035114}