{"uuid": "14a47c2d-532f-4d84-9cd2-16aca51e9ef2", "historyId": "7492be1be788139345c0cedb05f1dc9d:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > negativeLogin.spec.ts > Negative Login Test Cases"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "negativeLogin.spec.ts"}, {"name": "subSuite", "value": "Negative Login Test Cases"}], "links": [], "start": 1752188035108, "name": "TC-NEG-03: Login with both invalid username and password", "fullName": "negativeLogin.spec.ts#Negative Login Test Cases TC-NEG-03: Login with both invalid username and password", "testCaseId": "7492be1be788139345c0cedb05f1dc9d", "stop": 1752188035108}