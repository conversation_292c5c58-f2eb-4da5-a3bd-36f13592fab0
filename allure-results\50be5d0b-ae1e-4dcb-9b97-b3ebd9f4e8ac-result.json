{"uuid": "50be5d0b-ae1e-4dcb-9b97-b3ebd9f4e8ac", "historyId": "1f9ae9052803b9e592de33af239fa83e:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > negativeLogin.spec.ts > Negative Login Test Cases"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "negativeLogin.spec.ts"}, {"name": "subSuite", "value": "Negative Login Test Cases"}], "links": [], "start": 1752188035119, "name": "TC-NEG-11: Multiple rapid login attempts", "fullName": "negativeLogin.spec.ts#Negative Login Test Cases TC-NEG-11: Multiple rapid login attempts", "testCaseId": "1f9ae9052803b9e592de33af239fa83e", "stop": 1752188035119}