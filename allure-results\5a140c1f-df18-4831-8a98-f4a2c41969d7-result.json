{"uuid": "5a140c1f-df18-4831-8a98-f4a2c41969d7", "historyId": "d1da372de1ab48071f764af1b845c681:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > negativeLogin.spec.ts > Negative Login Test Cases"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "negativeLogin.spec.ts"}, {"name": "subSuite", "value": "Negative Login Test Cases"}], "links": [], "start": 1752188035113, "name": "TC-NEG-07: SQL Injection attempt in username", "fullName": "negativeLogin.spec.ts#Negative Login Test Cases TC-NEG-07: SQL Injection attempt in username", "testCaseId": "d1da372de1ab48071f764af1b845c681", "stop": 1752188035113}