{"uuid": "75654bdf-f0ed-469a-bf6a-eee5c59a8f23", "historyId": "eb92d3ad60de5857dce2d6b22e101126:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "broken", "statusDetails": {"message": "Test timeout of 10000ms exceeded while running \"beforeEach\" hook.", "trace": "Test timeout of 10000ms exceeded while running \"beforeEach\" hook.\n    at D:\\Playwright\\tests\\test.spec.ts:8:6"}, "stage": "pending", "steps": [{"status": "failed", "statusDetails": {"message": "Error: page.goto: Test timeout of 10000ms exceeded.\nCall log:\n  - navigating to \"https://opensource-demo.orangehrmlive.com/web/index.php/auth/login\", waiting until \"load\"\n", "trace": "Error: page.goto: Test timeout of 10000ms exceeded.\nCall log:\n  - navigating to \"https://opensource-demo.orangehrmlive.com/web/index.php/auth/login\", waiting until \"load\"\n\n    at Object.loadUrl (D:\\Playwright\\tests\\pages\\basePage.ts:14:16)\n    at D:\\Playwright\\tests\\test.spec.ts:12:20"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188104905, "name": "beforeAll hook", "stop": 1752188104964}, {"status": "failed", "statusDetails": {"message": "Error: page.goto: Test timeout of 10000ms exceeded.\nCall log:\n  - navigating to \"https://opensource-demo.orangehrmlive.com/web/index.php/auth/login\", waiting until \"load\"\n", "trace": "Error: page.goto: Test timeout of 10000ms exceeded.\nCall log:\n  - navigating to \"https://opensource-demo.orangehrmlive.com/web/index.php/auth/login\", waiting until \"load\"\n\n    at Object.loadUrl (D:\\Playwright\\tests\\pages\\basePage.ts:14:16)\n    at D:\\Playwright\\tests\\test.spec.ts:12:20"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188105033, "name": "browserType.launch", "stop": 1752188111622}], "attachments": [], "parameters": [], "start": 1752188105014, "name": "fixture: browser", "stop": 1752188111656}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188112965, "name": "browser.newContext", "stop": 1752188113993}], "attachments": [], "parameters": [], "start": 1752188112255, "name": "fixture: context", "stop": 1752188115044}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188115057, "name": "browserContext.newPage", "stop": 1752188117578}], "attachments": [], "parameters": [], "start": 1752188115049, "name": "fixture: page", "stop": 1752188117578}, {"status": "failed", "statusDetails": {"message": "Error: page.goto: Test timeout of 10000ms exceeded.\nCall log:\n  - navigating to \"https://opensource-demo.orangehrmlive.com/web/index.php/auth/login\", waiting until \"load\"\n", "trace": "Error: page.goto: Test timeout of 10000ms exceeded.\nCall log:\n  - navigating to \"https://opensource-demo.orangehrmlive.com/web/index.php/auth/login\", waiting until \"load\"\n\n    at Object.loadUrl (D:\\Playwright\\tests\\pages\\basePage.ts:14:16)\n    at D:\\Playwright\\tests\\test.spec.ts:12:20"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188117590, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1752188122549}], "attachments": [], "parameters": [], "start": 1752188104967, "name": "Create user", "stop": 1752188122669}], "attachments": [], "parameters": [], "start": 1752188104904, "name": "Before Hooks", "stop": 1752188122810}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188121591, "name": "page.screenshot", "stop": 1752188121917}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188121937, "name": "fixture: testRailIntegration", "stop": 1752188121938}], "attachments": [], "parameters": [], "start": 1752188121935, "name": "after<PERSON>ach hook", "stop": 1752188121938}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188121941, "name": "Delete user", "stop": 1752188122314}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188122331, "name": "fixture: testRailIntegration", "stop": 1752188122335}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188122367, "name": "fixture: page", "stop": 1752188122367}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188122367, "name": "fixture: context", "stop": 1752188122367}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125903, "name": "afterAll hook", "stop": 1752188127398}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188127399, "name": "fixture: browser", "stop": 1752188128392}], "attachments": [], "parameters": [], "start": 1752188121585, "name": "After Hooks", "stop": 1752188128392}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188124245, "name": "video.saveAs", "stop": 1752188124259}], "attachments": [{"name": "screenshot", "type": "image/png", "source": "3164af29-04b1-4d8a-85f2-1affcdcf8f15-attachment.png"}, {"name": "video", "type": "video/webm", "source": "3e9e8ddf-ee4b-4a92-9d4a-ddf0deb40df9-attachment.webm"}, {"name": "trace", "type": "application/zip", "source": "bf9d49ce-158c-476f-842b-6d3d6be34f40-attachment.zip"}, {"name": "stdout", "type": "text/plain", "source": "36ccb5be-ad96-425b-8582-7044181cabcf-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-29352-playwright-worker-2"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1752188104872, "name": "TC-03, Demo common functions usage", "fullName": "test.spec.ts#Login Tasks TC-03, Demo common functions usage", "testCaseId": "eb92d3ad60de5857dce2d6b22e101126", "stop": 1752188128425}