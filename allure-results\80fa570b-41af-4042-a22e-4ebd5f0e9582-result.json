{"uuid": "80fa570b-41af-4042-a22e-4ebd5f0e9582", "historyId": "57a3314179baa54736e7e6e1d9a39fcc:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "broken", "statusDetails": {"message": "Test timeout of 10000ms exceeded while running \"beforeEach\" hook.", "trace": "Test timeout of 10000ms exceeded while running \"beforeEach\" hook.\n    at D:\\Playwright\\tests\\test.spec.ts:8:6"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188104949, "name": "beforeAll hook", "stop": 1752188105591}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188105592, "name": "browserType.launch", "stop": 1752188114298}], "attachments": [], "parameters": [], "start": 1752188105592, "name": "fixture: browser", "stop": 1752188114299}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188114909, "name": "browser.newContext", "stop": 1752188115152}], "attachments": [], "parameters": [], "start": 1752188114795, "name": "fixture: context", "stop": 1752188115217}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188115228, "name": "browserContext.newPage", "stop": 1752188117428}], "attachments": [], "parameters": [], "start": 1752188115221, "name": "fixture: page", "stop": 1752188117428}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188117436, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1752188125067}], "attachments": [], "parameters": [], "start": 1752188105592, "name": "Create user", "stop": 1752188125067}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125067, "name": "fixture: testRailIntegration", "stop": 1752188125067}], "attachments": [], "parameters": [], "start": 1752188104939, "name": "Before Hooks", "stop": 1752188125067}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188124267, "name": "page.screenshot", "stop": 1752188125315}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125067, "name": "fixture: page", "stop": 1752188125067}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125068, "name": "fixture: context", "stop": 1752188125068}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125594, "name": "fixture: testRailIntegration", "stop": 1752188125596}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188126826, "name": "fixture: browser", "stop": 1752188128536}], "attachments": [], "parameters": [], "start": 1752188124262, "name": "After Hooks", "stop": 1752188128536}, {"status": "failed", "statusDetails": {"message": "expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "trace": "\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:31:34)\n    at D:\\Playwright\\tests\\test.spec.ts:27:25"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125067, "name": "expect.toBeVisible", "stop": 1752188125780}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125475, "name": "after<PERSON>ach hook", "stop": 1752188125476}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125477, "name": "Delete user", "stop": 1752188125592}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188126707, "name": "video.saveAs", "stop": 1752188126713}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188126821, "name": "afterAll hook", "stop": 1752188127386}], "attachments": [{"name": "screenshot", "type": "image/png", "source": "044ea230-df64-4215-9798-378d88730c63-attachment.png"}, {"name": "video", "type": "video/webm", "source": "5ab23f88-ba76-4ab8-9500-0a5ab0c909b5-attachment.webm"}, {"name": "trace", "type": "application/zip", "source": "4e892004-e238-4f3f-8bba-cd03240fe702-attachment.zip"}, {"name": "stdout", "type": "text/plain", "source": "9c906d22-6b37-4e36-a60e-d5ad9d5336d0-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-29352-playwright-worker-1"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1752188104904, "name": "TC-02: Dashboard elements verification", "fullName": "test.spec.ts#Login Tasks TC-02: Dashboard elements verification", "testCaseId": "57a3314179baa54736e7e6e1d9a39fcc", "stop": 1752188128561}