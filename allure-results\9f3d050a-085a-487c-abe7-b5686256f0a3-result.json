{"uuid": "9f3d050a-085a-487c-abe7-b5686256f0a3", "historyId": "b9365d2c3c0e664caf7c69fb1611cc1b:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > negativeLogin.spec.ts > Negative Login Test Cases"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "negativeLogin.spec.ts"}, {"name": "subSuite", "value": "Negative Login Test Cases"}], "links": [], "start": 1752188035109, "name": "TC-NEG-04: Login with empty username field", "fullName": "negativeLogin.spec.ts#Negative Login Test Cases TC-NEG-04: Login with empty username field", "testCaseId": "b9365d2c3c0e664caf7c69fb1611cc1b", "stop": 1752188035109}