{"uuid": "b94fac45-75d3-464e-9df7-bfac442fab0c", "historyId": "7ec117f72139e0371152a88f4989f75c:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > negativeLogin.spec.ts > Negative Login Test Cases"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "negativeLogin.spec.ts"}, {"name": "subSuite", "value": "Negative Login Test Cases"}], "links": [], "start": 1752188035107, "name": "TC-NEG-02: Login with valid username and invalid password", "fullName": "negativeLogin.spec.ts#Negative Login Test Cases TC-NEG-02: Login with valid username and invalid password", "testCaseId": "7ec117f72139e0371152a88f4989f75c", "stop": 1752188035107}