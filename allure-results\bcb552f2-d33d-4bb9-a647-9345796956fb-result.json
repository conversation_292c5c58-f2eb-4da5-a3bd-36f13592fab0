{"uuid": "bcb552f2-d33d-4bb9-a647-9345796956fb", "historyId": "79a16a46f77c4c17b5d7b3f7cbf1b419:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > negativeLogin.spec.ts > Negative Login Test Cases"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "negativeLogin.spec.ts"}, {"name": "subSuite", "value": "Negative Login Test Cases"}], "links": [], "start": 1752188035121, "name": "TC-NEG-12: Unicode and international characters", "fullName": "negativeLogin.spec.ts#Negative Login Test Cases TC-NEG-12: Unicode and international characters", "testCaseId": "79a16a46f77c4c17b5d7b3f7cbf1b419", "stop": 1752188035121}