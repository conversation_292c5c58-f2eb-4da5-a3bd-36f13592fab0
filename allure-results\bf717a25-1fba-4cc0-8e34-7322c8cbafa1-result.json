{"uuid": "bf717a25-1fba-4cc0-8e34-7322c8cbafa1", "historyId": "1c6427fda1bc34c81821617d1a5f4da0:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "broken", "statusDetails": {"message": "Test timeout of 10000ms exceeded while running \"beforeEach\" hook.", "trace": "Test timeout of 10000ms exceeded while running \"beforeEach\" hook.\n    at D:\\Playwright\\tests\\test.spec.ts:8:6"}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188104812, "name": "beforeAll hook", "stop": 1752188104874}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188104947, "name": "browserType.launch", "stop": 1752188113072}], "attachments": [], "parameters": [], "start": 1752188104934, "name": "fixture: browser", "stop": 1752188113372}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188114392, "name": "browser.newContext", "stop": 1752188115017}], "attachments": [], "parameters": [], "start": 1752188113588, "name": "fixture: context", "stop": 1752188115048}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188115060, "name": "browserContext.newPage", "stop": 1752188116883}], "attachments": [], "parameters": [], "start": 1752188115052, "name": "fixture: page", "stop": 1752188116886}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188117060, "name": "page.goto(https://opensource-demo.orangehrmlive.com/web/index.php/auth/login)", "stop": 1752188124152}], "attachments": [], "parameters": [], "start": 1752188104876, "name": "Create user", "stop": 1752188124152}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188124154, "name": "fixture: testRailIntegration", "stop": 1752188124156}], "attachments": [], "parameters": [], "start": 1752188104809, "name": "Before Hooks", "stop": 1752188124156}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [{"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188123408, "name": "page.screenshot", "stop": 1752188124865}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188124156, "name": "fixture: page", "stop": 1752188124156}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188124181, "name": "fixture: context", "stop": 1752188124181}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188125068, "name": "fixture: testRailIntegration", "stop": 1752188125068}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188127155, "name": "fixture: browser", "stop": 1752188128859}], "attachments": [], "parameters": [], "start": 1752188123362, "name": "After Hooks", "stop": 1752188128860}, {"status": "failed", "statusDetails": {"message": "expect(locator).toBeVisible()\n\nLocator: locator('[name = \\'username\\']')\nExpected: visible\nReceived: hidden\nCall log:\n  - expect.toBeVisible with timeout 5000ms\n  - waiting for locator('[name = \\'username\\']')\n", "trace": "\n    at Object.seeTheLoginPage (D:\\Playwright\\tests\\pages\\loginPage.ts:31:34)\n    at D:\\Playwright\\tests\\test.spec.ts:21:25"}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188124167, "name": "expect.toBeVisible", "stop": 1752188126194}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188124882, "name": "after<PERSON>ach hook", "stop": 1752188124882}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188124883, "name": "Delete user", "stop": 1752188125068}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188127073, "name": "video.saveAs", "stop": 1752188127074}, {"status": "passed", "statusDetails": {}, "stage": "pending", "steps": [], "attachments": [], "parameters": [], "start": 1752188127152, "name": "afterAll hook", "stop": 1752188127542}], "attachments": [{"name": "screenshot", "type": "image/png", "source": "9e95d1cd-f0b7-4458-971e-058828bc03e7-attachment.png"}, {"name": "video", "type": "video/webm", "source": "0baa3ca4-e158-4338-adfb-5b084e4953b6-attachment.webm"}, {"name": "trace", "type": "application/zip", "source": "37c3c5dc-fda5-4335-8a66-eb715f08b070-attachment.zip"}, {"name": "stdout", "type": "text/plain", "source": "bac086e1-308c-4fd8-8cad-5e45e1687239-attachment.txt"}], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-29352-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1752188104785, "name": "TC-01: Basic login functionality", "fullName": "test.spec.ts#Login Tasks TC-01: Basic login functionality", "testCaseId": "1c6427fda1bc34c81821617d1a5f4da0", "stop": 1752188128888}