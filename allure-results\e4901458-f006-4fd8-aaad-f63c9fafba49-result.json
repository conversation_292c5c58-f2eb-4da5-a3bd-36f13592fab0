{"uuid": "e4901458-f006-4fd8-aaad-f63c9fafba49", "historyId": "c8cd6da735b4349b055196e827fa8b49:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > negativeLogin.spec.ts > Negative Login Test Cases"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "negativeLogin.spec.ts"}, {"name": "subSuite", "value": "Negative Login Test Cases"}], "links": [], "start": 1752188035105, "name": "TC-NEG-01: Login with invalid username and valid password", "fullName": "negativeLogin.spec.ts#Negative Login Test Cases TC-NEG-01: Login with invalid username and valid password", "testCaseId": "c8cd6da735b4349b055196e827fa8b49", "stop": 1752188035105}