{"uuid": "eac10046-824e-414d-bdad-26edb0bec265", "historyId": "1c6427fda1bc34c81821617d1a5f4da0:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {"message": "This test was skipped due to test setup error. Check you setup scripts to fix the issue."}, "stage": "pending", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test.spec.ts > Login Tasks"}, {"name": "ALLURE_ID", "value": "-1"}, {"name": "host", "value": "LHR-Tufail-Khan1-EWIN"}, {"name": "thread", "value": "LHR-<PERSON><PERSON><PERSON>-Khan1-EWIN-19396-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "test.spec.ts"}, {"name": "subSuite", "value": "Login Tasks"}], "links": [], "start": 1752188035122, "name": "TC-01: Basic login functionality", "fullName": "test.spec.ts#Login Tasks TC-01: Basic login functionality", "testCaseId": "1c6427fda1bc34c81821617d1a5f4da0", "stop": 1752188035122}