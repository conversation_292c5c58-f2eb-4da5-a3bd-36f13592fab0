import { test as base, TestInfo } from '@playwright/test'
import { TestRailIntegration, TestResult } from './tests/utils/testRailIntegration'

// Global TestRail integration instance
let testRailIntegration: TestRailIntegration | null = null;

// Initialize TestRail integration once
async function getTestRailIntegration(): Promise<TestRailIntegration> {
  if (!testRailIntegration) {
    testRailIntegration = new TestRailIntegration();
    await testRailIntegration.initialize();
  }
  return testRailIntegration;
}

// Extended fixture type that includes TestRail
type ExtendedFixtures = {
  testRailIntegration: TestRailIntegration;
};

export const test = base.extend<ExtendedFixtures>({
  // TestRail integration fixture
  testRailIntegration: async ({}, use: (r: TestRailIntegration) => Promise<void>, testInfo: TestInfo) => {
    const integration = await getTestRailIntegration();

    // Record test start
    integration.onTestStart(testInfo);

    await use(integration);
  },
});

// Global setup and teardown hooks
test.beforeAll(async () => {
  console.log('[TestRail Integration] Starting test suite...');
  await getTestRailIntegration();
});

test.afterAll(async () => {
  console.log('[TestRail Integration] Test suite completed, finalizing results...');
  if (testRailIntegration) {
    try {
      await testRailIntegration.finalizeResults();
    } catch (error) {
      console.error('[TestRail Integration] Failed to finalize results:', error);
    }
  }
});

// Individual test hooks
test.afterEach(async ({ testRailIntegration }, testInfo: TestInfo) => {
  // Record test completion
  const result: TestResult = {
    status: (testInfo.status === 'interrupted' ? 'failed' : testInfo.status) || 'skipped',
    duration: testInfo.duration || 0,
    error: testInfo.error ? new Error(testInfo.error.message || 'Test failed') : undefined,
    startTime: new Date(), // TestInfo doesn't have startTime, so we use current time
    attachments: testInfo.attachments || []
  };

  testRailIntegration.onTestEnd(testInfo, result);
});