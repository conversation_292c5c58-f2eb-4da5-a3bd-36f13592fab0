{"name": "Playwright", "lockfileVersion": 3, "requires": true, "packages": {"": {"devDependencies": {"@playwright/test": "^1.40.1", "@types/node": "^20.10.4", "allure-commandline": "^2.34.0", "allure-playwright": "^2.10.0", "playwright": "1.40.1"}}, "node_modules/@playwright/test": {"version": "1.40.1", "resolved": "https://registry.npmjs.org/@playwright/test/-/test-1.40.1.tgz", "integrity": "sha512-EaaawMTOeEItCRvfmkI9v6rBkF1svM8wjl/YPRrg2N2Wmp+4qJYkWtJsbew1szfKKDm6fPLy4YAanBhIlf9dWw==", "dev": true, "dependencies": {"playwright": "1.40.1"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=16"}}, "node_modules/@types/node": {"version": "20.10.4", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.10.4.tgz", "integrity": "sha512-D08YG6rr8X90YB56tSIuBaddy/UXAA9RKJoFvrsnogAum/0pmjkgi4+2nx96A330FmioegBWmEYQ+syqCFaveg==", "dev": true, "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/allure-commandline": {"version": "2.34.0", "resolved": "https://registry.npmjs.org/allure-commandline/-/allure-commandline-2.34.0.tgz", "integrity": "sha512-+SgVJ9+o7OH43KVCln0KT7VIsXCfMVEvFuYArIFNtS0fWu61UFdeiCYvrlQPC9trEoFhOe2e5i8Xhghwo46iRQ==", "dev": true, "license": "Apache-2.0", "bin": {"allure": "bin/allure"}}, "node_modules/allure-js-commons": {"version": "2.10.0", "resolved": "https://registry.npmjs.org/allure-js-commons/-/allure-js-commons-2.10.0.tgz", "integrity": "sha512-DgACWBU2dchQD8tQOo5Y0MXx08SSzdgCnKBdwrOu29vITYBXih+0r8SbmrFYQhjAbn8eKMM+mXq+rKtjZRa2oA==", "dev": true, "dependencies": {"properties": "^1.2.1"}}, "node_modules/allure-playwright": {"version": "2.10.0", "resolved": "https://registry.npmjs.org/allure-playwright/-/allure-playwright-2.10.0.tgz", "integrity": "sha512-zDljPJ/Fnyd2fn7msChtZebwkSVmAGLe/oWK7okGi0Ed+iHZ0E5Vwe5Z5MtUdfLjnT/OkOduwmS7R/DHeTXFSA==", "dev": true, "dependencies": {"allure-js-commons": "2.10.0"}}, "node_modules/playwright": {"version": "1.40.1", "resolved": "https://registry.npmjs.org/playwright/-/playwright-1.40.1.tgz", "integrity": "sha512-2eHI7IioIpQ0bS1Ovg/HszsN/XKNwEG1kbzSDDmADpclKc7CyqkHw7Mg2JCz/bbCxg25QUPcjksoMW7JcIFQmw==", "dev": true, "dependencies": {"playwright-core": "1.40.1"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=16"}, "optionalDependencies": {"fsevents": "2.3.2"}}, "node_modules/playwright-core": {"version": "1.40.1", "resolved": "https://registry.npmjs.org/playwright-core/-/playwright-core-1.40.1.tgz", "integrity": "sha512-+hkOycxPiV534c4HhpfX6yrlawqVUzITRKwHAmYfmsVreltEl6fAZJ3DPfLMOODw0H3s1Itd6MDCWmP1fl/QvQ==", "dev": true, "bin": {"playwright-core": "cli.js"}, "engines": {"node": ">=16"}}, "node_modules/properties": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/properties/-/properties-1.2.1.tgz", "integrity": "sha512-qYNxyMj1JeW54i/EWEFsM1cVwxJbtgPp8+0Wg9XjNaK6VE/c4oRi6PNu5p7w1mNXEIQIjV5Wwn8v8Gz82/QzdQ==", "dev": true, "engines": {"node": ">=0.10"}}, "node_modules/undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "dev": true}}}