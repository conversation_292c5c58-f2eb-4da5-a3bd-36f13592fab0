import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  // reporter: 'html',
  reporter: [["line"], ["allure-playwright"]],


  use: {
    launchOptions: {
      args: ["--start-maximized"],
      headless: false,
    },
  },

  /* Configure projects for multiple browsers */
  projects: [
    {
      name: 'chromium',
      use: {
        viewport: null,
      },
    },

    // {
    //   name: 'firefox',
    //   use: {
    //     viewport: null,
    //   },
    // },

    // {
    //   name: 'webkit',
    //   use: {
    //     viewport: null,
    //   },
    // },
  ],
});
