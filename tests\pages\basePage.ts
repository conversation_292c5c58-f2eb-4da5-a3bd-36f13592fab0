import { expect, Locator, Page } from '@playwright/test';


type BasePage = {
  loadUrl(
    page: Page
  ): Promise<void>;
  enterLoginDetails(page: Page): Promise<void>;
};

const basePage: BasePage = {
  async loadUrl(
    page: Page,
  ): Promise<void> {
    await page.goto("https://opensource-demo.orangehrmlive.com/web/index.php/auth/login");
   
  },

  async enterLoginDetails(page): Promise<void>{
    await page.goto("https://opensource-demo.orangehrmlive.com/web/index.php/auth/login");
        const title = await page.title();
        console.log('Page title:', title);
        // await page.pause()

        await page.getByText('Login').click();
  }
  

};

export default basePage;
