import { Locator, expect } from '@playwright/test';

/**
 * Common Functions Second Page - Utility functions for Playwright tests
 * This file contains reusable utility functions that work directly with Locator objects
 */

/**
 * Retrieves the text content of the provided locator
 * @param locator - Playwright Locator object
 * @returns Promise<string> - The text content of the element
 * @throws Error if the element is not found or not visible
 */
export async function getText(locator: Locator): Promise<string> {
    try {
        // Wait for the element to be visible before getting text
        await locator.waitFor({ state: 'visible', timeout: 5000 });
        
        // Get the text content and trim whitespace
        const text = await locator.textContent();
        
        if (text === null) {
            throw new Error(`No text content found for locator: ${locator}`);
        }
        
        const trimmedText = text.trim();
        console.log(`Retrieved text: "${trimmedText}" from locator: ${locator}`);
        
        return trimmedText;
    } catch (error) {
        console.error(`Failed to get text from locator: ${locator}`, error);
        throw new Error(`Unable to retrieve text from element: ${error}`);
    }
}

/**
 * Types the provided text into the given locator
 * @param locator - Playwright Locator object (should be an input field)
 * @param text - Text to type into the element
 * @returns Promise<void>
 * @throws Error if the element is not found, not visible, or not editable
 */
export async function addText(locator: Locator, text: string): Promise<void> {
    try {
        // Wait for the element to be visible and attached
        await locator.waitFor({ state: 'visible', timeout: 5000 });
        
        // Ensure the element is editable
        const isEditable = await locator.isEditable();
        if (!isEditable) {
            throw new Error(`Element is not editable: ${locator}`);
        }
        
        // Clear existing content and fill with new text
        await locator.clear();
        await locator.fill(text);
        
        // Verify the text was entered correctly
        const enteredValue = await locator.inputValue();
        if (enteredValue !== text) {
            console.warn(`Warning: Expected "${text}" but got "${enteredValue}"`);
        }
        
        console.log(`Successfully entered text: "${text}" into locator: ${locator}`);
    } catch (error) {
        console.error(`Failed to add text to locator: ${locator}`, error);
        throw new Error(`Unable to enter text "${text}" into element: ${error}`);
    }
}

/**
 * Waits for the locator to become visible
 * @param locator - Playwright Locator object
 * @param timeout - Optional timeout in milliseconds (default: 5000)
 * @returns Promise<void>
 * @throws Error if the element doesn't become visible within the timeout
 */
export async function waitForVisible(locator: Locator, timeout: number = 5000): Promise<void> {
    try {
        await locator.waitFor({ 
            state: 'visible', 
            timeout: timeout 
        });
        
        console.log(`Element is now visible: ${locator} (waited up to ${timeout}ms)`);
    } catch (error) {
        console.error(`Element did not become visible within ${timeout}ms: ${locator}`, error);
        throw new Error(`Timeout waiting for element to be visible: ${error}`);
    }
}

/**
 * Scrolls down the page until the given locator is in view
 * @param locator - Playwright Locator object to scroll to
 * @returns Promise<void>
 * @throws Error if the element cannot be scrolled into view
 */
export async function scrollToLocator(locator: Locator): Promise<void> {
    try {
        // First, wait for the element to be attached to the DOM
        await locator.waitFor({ state: 'attached', timeout: 5000 });
        
        // Scroll the element into view if needed
        await locator.scrollIntoViewIfNeeded();
        
        // Wait a brief moment for the scroll to complete
        await locator.page().waitForTimeout(500);
        
        // Verify the element is now visible
        const isVisible = await locator.isVisible();
        if (!isVisible) {
            console.warn(`Warning: Element may not be fully visible after scrolling: ${locator}`);
        }
        
        console.log(`Successfully scrolled to locator: ${locator}`);
    } catch (error) {
        console.error(`Failed to scroll to locator: ${locator}`, error);
        throw new Error(`Unable to scroll to element: ${error}`);
    }
}

/**
 * Additional utility function: Check if element is visible
 * @param locator - Playwright Locator object
 * @returns Promise<boolean> - True if element is visible, false otherwise
 */
export async function isElementVisible(locator: Locator): Promise<boolean> {
    try {
        await locator.waitFor({ state: 'visible', timeout: 1000 });
        return true;
    } catch {
        return false;
    }
}

/**
 * Additional utility function: Get element count
 * @param locator - Playwright Locator object
 * @returns Promise<number> - Number of matching elements
 */
export async function getElementCount(locator: Locator): Promise<number> {
    try {
        const count = await locator.count();
        console.log(`Found ${count} elements matching locator: ${locator}`);
        return count;
    } catch (error) {
        console.error(`Failed to count elements for locator: ${locator}`, error);
        return 0;
    }
}

/**
 * Additional utility function: Wait and click element
 * @param locator - Playwright Locator object
 * @param timeout - Optional timeout in milliseconds (default: 5000)
 * @returns Promise<void>
 */
export async function waitAndClick(locator: Locator, timeout: number = 5000): Promise<void> {
    try {
        await waitForVisible(locator, timeout);
        await locator.click();
        console.log(`Successfully clicked on locator: ${locator}`);
    } catch (error) {
        console.error(`Failed to click on locator: ${locator}`, error);
        throw new Error(`Unable to click element: ${error}`);
    }
}

// Export all functions as a default object for convenience
export default {
    getText,
    addText,
    waitForVisible,
    scrollToLocator,
    isElementVisible,
    getElementCount,
    waitAndClick
};
