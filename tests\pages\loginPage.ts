import { expect, Locator, Page } from '@playwright/test';


type LoginPage = {
    seeTheLoginPage(
    page: Page
  ): Promise<void>;
  enterLoginDetails(page: Page): Promise<void>;
};

const loginPage: LoginPage = {
  async seeTheLoginPage(
    page: Page,
    ): Promise<void> {
        let emailField: Locator = page.locator("[name = 'username']");
        let passwordField: Locator = page.locator("[name='password']");
        let loginBtn: Locator = page.getByRole('button', {
            name: "Login",
            exact: true,
        });
    

        await expect(emailField).toBeVisible();
        await expect(passwordField).toBeVisible();
        await expect(loginBtn).toBeVisible();
    },

  async enterLoginDetails(page: Page): Promise<void> {
        let emailField: Locator = page.locator("[name = 'username']");
        let passwordField: Locator = page.locator("[name='password']");
        let loginBtn: Locator = page.getByRole('button', {
            name: "Login",
            exact: true,
        });

        await emailField.fill("Admin");
        await passwordField.fill("admin123");
        await loginBtn.click();
        await page.waitForTimeout(3000)

        let dashboardTitle: Locator = page.getByRole('heading', {
          name: "Dashboard",
          exact: true,
      });
      
      await expect(dashboardTitle).toBeVisible();
    }  

};

export default loginPage;
