import { test, expect } from '@playwright/test';
import basePage from './pages/basePage';
import loginPage from './pages/loginPage';


test.beforeEach('Create user', async ({
    page,
  }): Promise<void> => {
    console.log(`Creating user for ${test.info().title}`);
    await basePage.loadUrl(page);
});

test.afterEach('Delete user', async (): Promise<void> => {
    console.log(`${test.info().title} ${test.info().status}`);
});

test.describe('Login Tasks', () => {
    test('TC-01, Login to the URL', async ({ page }) => {
        await loginPage.seeTheLoginPage(page);
        await loginPage.enterLoginDetails(page);
    });

    test('TC-02, Verify dashboard elements after login', async ({ page }) => {
        // First login to the application
        await loginPage.seeTheLoginPage(page);
        await loginPage.enterLoginDetails(page);

        // Verify dashboard elements
        const dashboardTitle = page.getByRole('heading', {
            name: "Dashboard",
            exact: true,
        });
        await expect(dashboardTitle).toBeVisible();

        // Verify user profile menu is visible
        const userDropdown = page.locator('.oxd-userdropdown-tab');
        await expect(userDropdown).toBeVisible();

        // Verify main menu items
        const mainMenu = page.locator('ul.oxd-main-menu');
        await expect(mainMenu).toBeVisible();

        // Take a screenshot of the dashboard
        await page.screenshot({ path: 'dashboard.png' });
    });
});
