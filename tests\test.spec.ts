import { test, expect } from '@playwright/test';
import basePage from './pages/basePage';
import loginPage from './pages/loginPage';
import commonFunctions from './pages/commonFunctions';


test.beforeEach('Create user', async ({
    page,
  }): Promise<void> => {
    console.log(`Creating user for ${test.info().title}`);
    await basePage.loadUrl(page);
});

test.afterEach('Delete user', async (): Promise<void> => {
    console.log(`${test.info().title} ${test.info().status}`);
});

test.describe('Login Tasks', () => {
    test('TC-01, Login to the URL', async ({ page }) => {
        await loginPage.seeTheLoginPage(page);
        await loginPage.enterLoginDetails(page);
    });

    test('TC-02, Verify dashboard elements after login', async ({ page }) => {
        // First login to the application
        await loginPage.seeTheLoginPage(page);
        await loginPage.enterLoginDetails(page);

        // Verify dashboard elements using common functions
        await commonFunctions.waitForElementVisible(page, 'h6:has-text("Dashboard")');

        // Read dashboard title text
        const dashboardText = await commonFunctions.readText(page, 'h6:has-text("Dashboard")');
        console.log(`Dashboard title: ${dashboardText}`);

        // Scroll to user dropdown and verify visibility
        await commonFunctions.scrollToElement(page, '.oxd-userdropdown-tab');
        const isDropdownVisible = await commonFunctions.isElementVisible(page, '.oxd-userdropdown-tab');
        expect(isDropdownVisible).toBe(true);

        // Verify main menu items
        const mainMenu = page.locator('.oxd-main-menu');
        await expect(mainMenu).toBeVisible();

        // Take a screenshot of the dashboard
        await page.screenshot({ path: 'dashboard.png' });
    });

    test('TC-03, Demo common functions usage', async ({ page }) => {
        // Navigate to login page
        await basePage.loadUrl(page);

        // Use common functions for login
        await commonFunctions.enterText(page, "[name='username']", "Admin");
        await commonFunctions.enterText(page, "[name='password']", "admin123");
        await commonFunctions.clickElement(page, "button[type='submit']");

        // Wait for dashboard and scroll to an element
        await commonFunctions.waitForElementVisible(page, 'h6:has-text("Dashboard")');
        await commonFunctions.scrollToElement(page, '.oxd-userdropdown-tab');

        // Read some text from the page
        const welcomeText = await commonFunctions.readText(page, '.oxd-topbar-header-breadcrumb h6');
        console.log(`Welcome text: ${welcomeText}`);
    });
});
