{"id": "b097cea0-460d-4b8c-b4fe-091ca8c1da38", "name": "ItenereEnv", "values": [{"key": "ResponseTime", "value": "3000", "type": "default", "enabled": true}, {"key": "Country", "value": "Brazil", "type": "default", "enabled": true}, {"key": "country_id", "value": 30, "type": "any", "enabled": true}, {"key": "address", "value": "block25", "type": "default", "enabled": true}, {"key": "dialcode", "value": "+55", "type": "any", "enabled": true}, {"key": "registrationNumber", "value": "15.100.842/6547-05", "type": "any", "enabled": true}, {"key": "contactNumber", "value": "+551130446571", "type": "any", "enabled": true}, {"key": "email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "password", "value": "Admin123!", "type": "default", "enabled": true}, {"key": "OTP", "value": "4447", "type": "any", "enabled": true}, {"key": "gcpurl", "value": "https://identitytoolkit.googleapis.com", "type": "default", "enabled": true}, {"key": "projectkey", "value": "AIzaSyDK23bdi4gOY3ncpUH-TfZm3PwuBW_DEAE", "type": "default", "enabled": true}, {"key": "auth_token", "value": "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNjVjY2I4ZWFkMGJhZWY1ZmQzNjE5NWQ2NTI4YTA1NGZiYjc2ZjMiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RdIcOe-UIv8aJAewQPHOvUija2B5V1YUow5LSmRMZmZQZEVvP1-tC-iepy0u1fbyH7L0iiqgJdv5DVFKjsF1XFPZja3g0DrGP5smKkokai4hVp4pJHaPrlIJdle5GW2JGkGwcb5SfpMmQ4W91fXJRm1vz6LznL5LDGotnR5bJBF_Y_OP0qZxuQMpe9wOec823qHmcq5qexfywgUTT20tyW9cP0O2adLkEGWTRJwpovB2R_-hBmOWMu_rYUClYrNbVY04fA4qh2Yk7N1QlXycxY0Gpcci4tvcpbwKw7EkDpgAKeQTrDk9netoWIRiVlOJERR8asM5O1m3CTYFN8Zz6A", "type": "any", "enabled": true}, {"key": "emailagent", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "agency_registrationNumber", "value": 248, "type": "any", "enabled": true}, {"key": "agentID", "value": "221", "type": "any", "enabled": true}, {"key": "urltrip", "value": "https://api-gateway-qa-************.us-central1.run.app/trip-management", "type": "default", "enabled": true}, {"key": "url", "value": "https://agency-management-service-qa-************.us-central1.run.app/agency-management", "type": "default", "enabled": true}, {"key": "role", "value": "ROLE_ADMIN", "type": "default", "enabled": true}, {"key": "tempurl", "value": "https://agency-management-service-dev-************.us-central1.run.app/agency-management", "type": "default", "enabled": true}, {"key": "tempurlagency", "value": "https://agency-management-service-dev-************.us-central1.run.app/", "type": "default", "enabled": true}, {"key": "TermsType", "value": "signup", "type": "default", "enabled": true}, {"key": "TermsLocale", "value": "en", "type": "default", "enabled": true}, {"key": "Verify_email_url", "value": "https://api-gateway-dev-************.us-central1.run.app", "type": "default", "enabled": true}, {"key": "CompanyEmailAutomation", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "cookieValue", "value": "", "type": "any", "enabled": true}, {"key": "authToken", "value": "", "type": "any", "enabled": true}, {"key": "CustomerId", "value": "", "type": "any", "enabled": true}, {"key": "u<PERSON><PERSON><PERSON><PERSON>", "value": "https://traveler-management-service-qa-************.us-central1.run.app/traveler-management", "type": "default", "enabled": true}, {"key": "tripID", "value": "", "type": "any", "enabled": true}, {"key": "urlCustomer", "value": "https://api-gateway-qa-************.us-central1.run.app/customer-management", "type": "default", "enabled": true}, {"key": "randomAccommodationPreferenceID", "value": "", "type": "any", "enabled": true}, {"key": "randomAirTravelPreferenceID", "value": "", "type": "any", "enabled": true}, {"key": "<PERSON><PERSON><PERSON>", "value": "https://person-management-service-qa-************.us-central1.run.app/person-management", "type": "default", "enabled": true}, {"key": "url<PERSON><PERSON><PERSON>", "value": "https://api-gateway-qa-************.us-central1.run.app/customer-management", "type": "default", "enabled": true}, {"key": "baseUrl", "value": "http://customer-management-service-dev-************.us-central1.run.app/customer-management/api/v1/100/customers", "type": "default", "enabled": true}, {"key": "custName", "value": "", "type": "any", "enabled": true}, {"key": "custEmail", "value": "", "type": "any", "enabled": true}, {"key": "travelerId", "value": "464", "type": "any", "enabled": true}, {"key": "randomTravelerName", "value": "", "type": "default", "enabled": true}, {"key": "CustId", "value": "", "type": "any", "enabled": true}, {"key": "extractedRelatedTravelerId", "value": "474", "type": "any", "enabled": true}, {"key": "savedTravelerId", "value": "", "type": "any", "enabled": true}, {"key": "urlTripOp", "value": "http://accommodation-management-service-qa-************.us-central1.run.app/accommodation-management", "type": "default", "enabled": true}, {"key": "flightLegsId", "value": "", "type": "any", "enabled": true}, {"key": "urlDocument", "value": "https://api-gateway-qa-************.us-central1.run.app/document-management", "type": "default", "enabled": true}, {"key": "custId", "value": "", "type": "any", "enabled": true}, {"key": "leadTravelerEmail", "value": "null", "type": "any", "enabled": true}, {"key": "flightId", "value": "37", "type": "default", "enabled": true}, {"key": "passId", "value": "52", "type": "any", "enabled": true}, {"key": "NID", "value": "", "type": "any", "enabled": true}, {"key": "currentDateTime", "value": "2025-05-21T09:35:24.676Z", "type": "any", "enabled": true}, {"key": "testCycleKey", "value": "IHTC-R50", "type": "any", "enabled": true}, {"key": "projectKey", "value": "IHTC", "type": "default", "enabled": true}, {"key": "zephyrApiToken", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6kJDrzwz0i26P_odWYc_xx7dJ-4JJp7FpPXtz7Uuuu4", "type": "default", "enabled": true}, {"key": "meAgentID", "value": "", "type": "any", "enabled": true}, {"key": "runFinalExecution", "value": "true", "type": "any", "enabled": true}, {"key": "zephyrExecutionData", "value": "{\"projectKey\":\"IHTC\",\"testCycleKey\":\"CYCLE-123\",\"testCaseKey\":\"IHTC-T\",\"statusName\":\"Fail\",\"comment\":\"Mobility details are not present in the response body\"}", "type": "any", "enabled": true}, {"key": "user_id", "value": "", "type": "any", "enabled": true}, {"key": "flightLeg", "value": "", "type": "any", "enabled": true}, {"key": "mobID", "value": 54, "type": "default", "enabled": true}, {"key": "randomtransportType", "value": "randomtransportType", "type": "default", "enabled": true}, {"key": "contentIDs", "value": "", "type": "any", "enabled": true}, {"key": "zephyrAuthToken", "value": "Bearer YOUR_ZEPHYR_AUTH_TOKEN", "type": "any", "enabled": true}, {"key": "testResult", "value": "Pass", "type": "any", "enabled": true}, {"key": "randomBankAccountName", "value": "ACC5804633575", "type": "any", "enabled": true}, {"key": "randomPrice", "value": "375.07", "type": "any", "enabled": true}, {"key": "randomTransactionType", "value": "Debit Card", "type": "any", "enabled": true}, {"key": "randomFirstName", "value": "<PERSON>", "type": "any", "enabled": true}, {"key": "TravelerEmail", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "documentUrl", "value": "https://assets-qa.itinerehub.ai/trip-services/mobility/trip/132/Mobilityyy.png", "type": "any", "enabled": true}, {"key": "bookingReference", "value": "AS12344", "type": "any", "enabled": true}, {"key": "LeadTravelerEmailAutomation", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "CustomerEmailAutomation", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "AgentIdAutomation", "value": 541, "type": "any", "enabled": true}, {"key": "ProcessedCustomerIdAutomation", "value": 651, "type": "any", "enabled": true}, {"key": "TravelerIdAutomation", "value": 1009, "type": "any", "enabled": true}, {"key": "TravelerEmailAutomation", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "RelationshipIdAutomation", "value": 62, "type": "any", "enabled": true}, {"key": "PreferenceIdAutomation", "value": 70, "type": "any", "enabled": true}, {"key": "VisaIdAutomation", "value": 88, "type": "any", "enabled": true}, {"key": "RequestPassportNumber", "value": "{{$randomAlphaNumeric}}{{$randomBankAccount}}", "type": "any", "enabled": true}, {"key": "RequestDocument", "value": "string", "type": "any", "enabled": true}, {"key": "PassportIdAutomation", "value": 161, "type": "any", "enabled": true}, {"key": "expenseUrl", "value": "http://expense-management-service-dev-************.us-central1.run.app/expense-management/", "type": "default", "enabled": true}, {"key": "RequestIdNumber", "value": "e3f6ec7a-0445-450d-a574-9c3ca98e7448", "type": "any", "enabled": true}, {"key": "IdAutomation", "value": 216, "type": "any", "enabled": true}, {"key": "TripIdAutomation", "value": 477, "type": "any", "enabled": true}, {"key": "flightUrl", "value": "", "type": "default", "enabled": true}, {"key": "addedTravelerToTrip_DataId", "value": 207, "type": "any", "enabled": true}, {"key": "accomodationUrl", "value": "https://api-gateway-qa-************.us-central1.run.app/accommodation-management", "type": "default", "enabled": true}, {"key": "transportUrl", "value": "https://api-gateway-qa-************.us-central1.run.app/transport-management", "type": "default", "enabled": true}, {"key": "agencyID", "value": "72.100.532/5734-05", "type": "any", "enabled": true}, {"key": "FeedbackIdFromResponse", "value": 170, "type": "any", "enabled": true}, {"key": "FeedbackTripIdFromResponse", "value": "480", "type": "any", "enabled": true}, {"key": "zephyrExecutionData_Feedback", "value": "{\"projectKey\":\"IHTC\",\"testCycleKey\":\"CYCLE-123\",\"testCaseKey\":\"IHTC-T696\",\"statusName\":\"Fail\",\"comment\":\"Failed post-request tests: Feedback JSON response structure or data type validation failed. Details: 'data.internalComments' type: expected null to be a string\"}", "type": "any", "enabled": true}, {"key": "generatedNumbers", "value": "[\"+554513612887\",\"+555977269450\",\"+559116955893\",\"+551483865553\",\"+553426395638\",\"+552594609061\",\"+556082796959\",\"+554599596817\",\"+553153330667\",\"+555859994486\",\"+551093979461\",\"+555262250769\",\"+559516570066\",\"+555035782457\",\"+554133171882\",\"+554677997849\",\"+552185025623\",\"+558907657254\",\"+557638340765\",\"+559826255627\",\"+557658037873\",\"+554383535187\",\"+551968433479\",\"+551882280462\",\"+554713794319\",\"+554443241321\",\"+559410556516\",\"+557242055079\",\"+556967574893\",\"+557316928149\",\"+551400725391\",\"+555152021853\",\"+558238731578\",\"+551469775914\",\"+558894174414\",\"+553459853471\",\"+558787360217\",\"+551130446571\"]", "type": "any", "enabled": true}, {"key": "mobilityUrl", "value": "https://api-gateway-qa-************.us-central1.run.app/mobility-management", "type": "default", "enabled": true}, {"key": "travelerIds", "value": "", "type": "any", "enabled": true}, {"key": "passportNumber", "value": "", "type": "any", "enabled": true}, {"key": "randomFullName", "value": "", "type": "any", "enabled": true}, {"key": "zephyrExecutionResults", "value": "", "type": "any", "enabled": true}, {"key": "zephyrCurrentExecution", "value": "", "type": "any", "enabled": true}, {"key": "notiUrl", "value": "", "type": "default", "enabled": true}, {"key": "agencyName", "value": "", "type": "any", "enabled": true}, {"key": "agentId", "value": "", "type": "any", "enabled": true}, {"key": "personId", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-07-10T13:25:44.569Z", "_postman_exported_using": "Postman/11.53.1"}