import { Page, expect } from '@playwright/test';

export class TestHelpers {
    /**
     * Wait for element to be visible with custom timeout
     */
    static async waitForElement(page: Page, selector: string, timeout: number = 5000) {
        await page.waitForSelector(selector, { state: 'visible', timeout });
    }

    /**
     * Take screenshot with timestamp
     */
    static async takeScreenshot(page: Page, name: string) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        await page.screenshot({ 
            path: `screenshots/${name}-${timestamp}.png`,
            fullPage: true 
        });
    }

    /**
     * Verify page accessibility
     */
    static async checkAccessibility(page: Page) {
        // Check for basic accessibility requirements
        const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
        expect(headings).toBeGreaterThan(0);
        
        const images = await page.locator('img').count();
        if (images > 0) {
            const imagesWithAlt = await page.locator('img[alt]').count();
            expect(imagesWithAlt).toBe(images);
        }
    }

    /**
     * Performance monitoring
     */
    static async measurePageLoad(page: Page, url: string) {
        const startTime = Date.now();
        await page.goto(url);
        await page.waitForLoadState('networkidle');
        const loadTime = Date.now() - startTime;
        
        console.log(`Page load time: ${loadTime}ms`);
        expect(loadTime).toBeLessThan(10000); // Should load within 10 seconds
        
        return loadTime;
    }
}
